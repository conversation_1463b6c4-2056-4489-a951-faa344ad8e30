package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FolderPermissionVo {
    @ApiModelProperty("授权对象ID，可以是组，也可以是用户")
    private String ouid;
    @ApiModelProperty("授权对象名称")
    private String ouName;
    @ApiModelProperty("权限")
    private String ps;
    @ApiModelProperty("授权有效期")
    private String deadline;
    @ApiModelProperty("授权时间")
    private String setTime;
}
