server:
  port: 7064
  tomcat:
    max-swallow-size: -1
  servlet:
    context-path: /api
    compression:
      enabled: true
      mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

logging:
  config: classpath:logback-spring.xml

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

quartz:
  enable: false
  user: admin
  systemCode: scbzh

spring:
  main:
    allow-bean-definition-overriding: true
  jmx:
    default-domain: micro-app-yewu
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
      enabled: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    allow-request-override: false
    cache: false
    check-template-location: false
    charset: utf-8
    content-type: text/html; charset=utf-8
    suffix: .ftl
    template-loader-path: classpath:/templates/
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    hiddenmethod:
      filter:
        enabled: true
  resource:
    static-locations: classpath:/static/,classpath:/public/
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123QWE#@!*
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        #        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
        validation-query: select 1
      datasource:
        master:
          url: jdbc:dm://************:5236/?ZS_SCBZH_PRO&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&charset=utf-8&compatibleMode=oracle&clobAsString=true
          username: ZS_SCBZH_PRO
          password: star0822!
          driver-class-name: dm.jdbc.driver.DmDriver
          type: com.alibaba.druid.pool.DruidDataSource
        scbzhbzhgl:
          url: jdbc:dm://************:5236/?ZS_BZHGL_PRO&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&charset=utf-8&compatibleMode=oracle&clobAsString=true
          username: ZS_BZHGL_PRO
          password: star0822!
          driver-class-name: dm.jdbc.driver.DmDriver
          type: com.alibaba.druid.pool.DruidDataSource
  jpa:
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.Oracle8iDialect
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: ''
    port: 6379
  application:
    name: micro-app-yewu-ouyangjun

app:
  datasource:
    mapping:
      platform: master
      scbzhbzhgl: scbzhbzhgl

eureka:
  instance:
    prefer-ip-address: true
  client:
    register-with-eureka: true
    enabled: true
    fetch-registry: true
    service-url:
      defaultZone: http://************:20101/eureka/

#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:ltd/zstech/**/mapper/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: 4
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    #     这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true

#jeecg专用配置
jeecg:
  security:
    isEncrypted: false
    rsaPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDfqNRWw7TreqVvFjgY9lRrONAuReQsvmO3MIKqWyR51+3iZ/E96ocvNWJh5WfsXCTZG+a6GvM8HJ37tOaLRzO/sV26xtV42VT3qfkflw8edFPGjbX2dvmq4Oa8pe1ERAyOXHWM2PGRED3/MXkvCdJ55B8nivyMpUS6SZvPQh1BIm5RfraTY1GhWmjzATcSxADQnvmYlsrlWXYcMq8mx+Z94mtYvyZwwlSiuFmaUiXZPuO4XW9WfgXsUlAlUjw92jP5uXD0AaF1eU8j5n+FIA2+cUcKEzCD4BsdQmjl//xt1Dvpp3NNnJApAENkc9iydst9qUn3hZRijGOaJ+/FFaaDAgMBAAECggEBAL3k3xwnn8VK5YkSyWBQTM6YdxDCoH9oysrvchFnvumAbuYKsGWMaWion7ZUi06/0T1Qli4u0ZOZDH14U0Gvm31G/ziktvybMavxcaXjoJ+Pzs1G5lazHzl5KA7FplSOnkaEs/14+Gjc7HZuL+DDKyP4jocJV3GmpFqyQorPLH1uQiEMhI6+K+EWmMQjCIkFwDsWoKSPBIB6cj4dqNFzl/76KqWr1o5ePPP+eXwsIIEj0CI/QYq+dHd8taGcWQaeczJLF8ZmA29VCaurcBsPRSCoQZyt5yInPm013bTW8A5krFfoTcIcAwLHJkzoX2Vk84gs4mEhtYEPzo54hgPHR4kCgYEA/2v/y8BPg3loQ5cQGFXtNBIcsL16ZPjUnqCD6LAKmhmg4Zy7b5sA7j69EJTMTq0Thoy2LNii6/qkd06LF4W+VsqpnaaL1iSEMflceXCKWDIWx4AjQAsLVtGiBLQzPEuRXNp1MmW5Lhu5SJU2LIof6ps0SOZLwjfiAcIZgCsVnacCgYEA4CptC47Gl66OqhZwa4Kf1VbRU60QQadiMKqPb2hjRfdiBpTT3q+QxefkftfGT/uNPT2w7nafnNFHnO+FWFXSFktSrJI10kjomGKRErnBQSY/Qq4m7bAzEzb47cNek562hZTFkRctBa7zp6/3abSOGgjd6tTti/JDb/i8Nqueo8UCgYAXNHaeyi/vH96LFXRO4a+VJXgZzn0KI1kduDODtkqgSWfGV3jn4Msq2eRPV1VgaHT3qlz6ogvbKIA8npQgZPQv6mWSfwkBQXwJFkCCf+jxrktHgKqzLM2w64auqrhk5/Ci4O340CkMuVQR9gxf64SOyd7sKJAFaxJLWpj6fe+OzQKBgH7ihObN4ktpWwebu/ae8anogoRCqbj/7exuHtKI2+h2pt3i0Cp0ZhYftI63xGDxmnjXupbAPoidTVPfoDLoIQrNh/Z2rFBjRIU9BbD7xBAEfjpYKWDRI5f0veKXe6lDZT0ngM0nuSxGY1S/neQPd3/1FOgfrnZQoyt2N9MaQamhAoGBAPhcVs0nL0GWPFiLLzo8XV/6OYtdifnMx6VMD9gHX1si4w6qzLj4AqMZkTRcpSll4JOeMU/YIptG7tLnnHs2TNvVNDW/cT/ei7FKJVNalJfwGqPuIj2RO/AV3qSdoDr+i6U1a3e2CbsVLb2OPhEDy5XDobBsgtaQO4YzOtONjk3N
    rsaPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA36jUVsO063qlbxY4GPZUazjQLkXkLL5jtzCCqlskedft4mfxPeqHLzViYeVn7Fwk2RvmuhrzPByd+7Tmi0czv7FdusbVeNlU96n5H5cPHnRTxo219nb5quDmvKXtREQMjlx1jNjxkRA9/zF5LwnSeeQfJ4r8jKVEukmbz0IdQSJuUX62k2NRoVpo8wE3EsQA0J75mJbK5Vl2HDKvJsfmfeJrWL8mcMJUorhZmlIl2T7juF1vVn4F7FJQJVI8Pdoz+blw9AGhdXlPI+Z/hSANvnFHChMwg+AbHUJo5f/8bdQ76adzTZyQKQBDZHPYsnbLfalJ94WUYoxjmifvxRWmgwIDAQAB
  # 本地：local  阿里云：alioss
  uploadType: local
  path:
    #文件上传根目录 设置
    upload: /home/<USER>/upFiles
    #webapp文件路径
    webapp: D://webapp
    sysconfig: D://config
  #短信秘钥
  sms:
    accessKeyId: LTAIpW4gUG7xYDNI
    accessKeySecret: ???
  #阿里云oss存储配置
  oss:
    endpoint: oss-cn-shanghai.aliyuncs.com
    accessKey: LTAI5tQsYLBVoqZNocMsQ6Fo
    secretKey: ******************************
    bucketName: zstech
    staticDomain:
    #保存路径,前端不传biz则默认该路径 多层目录 zsboot/xxx/xxx
    defBiz: zsboot/core
  wdglUrl: http://*************:9090
  wdglPass: 12345678

#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas

zs:
  baseTables: AppUser,Employee,EnumInfo,FormNoManage,Organization,OrganizationEmployeeRelation,OrganizationRelation,Role,UserRoleRelation,CustomFormTemplateType
  definitionTables: CustomFormTemplate,WorkflowTemplate,CustomFormViews,CustomFormViewConfig
  companyCode: 2300

log:
  AppID: 546513
  LogLevel: INFO
  FileSizeLimitBytes: 100

wx:
  AgentId: 1000006
  Secret: 6TiPNoA6SBehXDYhj0wNnr3TefGTXRT-bZks8pl1c9k
  enterpriseID: wwe4531b2b23b25e31

dingding:
  Appkey: dingr5ddpcjvxicbrlt6
  Appsecret: 1YfSe4hiGksMXq9Uylw4MLrUrdoEHSn2vgnMOGfdS62iNw10Zc3WNnDYRef1HU0F
  AgentId: 1626508506
  CorpId: dinge756a63311be0a6035c2f4657eb6378f
  PushDataUrl: http://*************:9001

# 切换新老数据源后 ，要更改此配置， new 代表新  old代表老
dataSource:
  source: old
  candidates: ID,_TEMPLATE_ID, _ATTACHMENTS,_CONFIG_ID,_SUBJECT,_FORMNO,_HINT,_LEVEL_ID,
  pzTable: table_name_conversion
  #通过配置获取表字段，达梦 all_tab_columns,SqlServer:information_schema.columns
  columnsTable: all_tab_columns
  #是否需要个人中心菜单
  needPersonalCenter: true

appSettings:
  dataPermissionType: 1

providerUrl: http://************:7012/workflow

dingtalk:
  copyId: dinge756a63311be0a6035c2f4657eb6378f
  agentId: **********
  appKey: ding4o8e19cxz5ciyjwu
  appsecret: IUr50t3B-yccmTaFjH3sBMEZvaeIlBJ7JFa7gUUXHMUdqTGbupLY6rAhUHevc9ab
  tokenUrl: https://oapi.dingtalk.com/gettoken
  deptListUrl: https://oapi.dingtalk.com/topapi/v2/department/listsub
  userListUrl: https://oapi.dingtalk.com/topapi/v2/user/list
  sendMsgUrl: https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2
  msgUrl: http://d.pxmuxin.cn:20100/roamanMobile/#/FormProcessingPage?backpage=1&templateid=%s&formdataid=%s&workflow=true
  pcMsgUrl: http://d.pxmuxin.cn:20100/roamanpc/#/menu1/zFormView?templateId=%s&formdataId=%s&nvatitle=查看表单&readonly=true&workflow=true

flow:
  agree:
    list:
      - 同意
      - 确认
      - 关闭
      - 已完成
  disagree:
    list:
      - 不同意
      - 退回
      - 继续整改
      - 结束

special:
  chars: /,|,\\,，,*,:,",>,<,?,？,。,~,`,@,！,!,#,$,%,^,……,&,×,『,』,、

lockTimeoutMinutes: 5

loginCheck:
  minLength: 8 #最小长度
  hasUppercase: true #是否包含大写字母
  hasLowercase: true #是否包含小写字母
  hasDigit: false #是否包含数字
  hasSpecialChar: true #是否包含特殊字符
  lockOutMaxNum: 5 # 冻结最大次数,10
  lockOutTime: 120 # 冻结时长,30分钟
  updatePasswordDays: 30 # 密码修改周期, 30天
  forceLogout: false #是否强制登出
  checkPasswordType: 1 # 1.随机检验方式,  2.固定检验方式
  checkPasswordLevel: 2 # 满足两种即可
  defaultPassword: sncn123*
  decrypt:
    key: aeV9F8^Matcy5XkA
    iv: kr9VxV&tQ%j8kH73

safe:
  file:
    interceptType: 2
    suffixList:
      - .php
      - .bat
      - .sh
      - .msi
      - .com
      - .apk
      - .app
      - .vb
      - .wsf
      - .cmd
      - .jsp
      - .js
      - .bin
      - .msi
      - .cpl
      - .wsf
      - .war
      - .exe
      - .dll
      - .asp
      - .aspx
      - .jsp
      - .vba
      - .vbs
      - .vbe
      - .vbscript
      - .cpp
      - .o
  sql:
    enable: true
    enableKeyword: true
    fields:
      - templeid
      - templateid
      - configid
    intFields:
      - templateid
      - dataid
      - viewid
      - configid
  xss:
    enable: true
    regex: <script.*?>.*?</script.*?>|on\\w+\\s*=\\s*\".*?\"|on\\w+\\s*=\\s*'.*?'|<.*?>|<.*?>|javascript:.*
