package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserOfGroupVo implements Serializable {
    @ApiModelProperty("用户ID")
    private String uid;
    @ApiModelProperty("用户登录名称")
    private String name;
    @ApiModelProperty("用户昵称")
    private String nick;
    @ApiModelProperty("逗号分割的用户权限")
    private String ps;
    @ApiModelProperty("用户在此组中的排序值")
    private Integer order;
    @ApiModelProperty("授权截至时间，如果为空表示没有截至时间")
    private String deadline;

}
