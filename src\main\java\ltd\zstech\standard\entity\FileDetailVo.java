package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileDetailVo implements Serializable {
    @ApiModelProperty("文件ID")
    private String id;
    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("文件来源，（页面上传，数据导入）")
    private String from;
    @ApiModelProperty("文件修改时间")
    private String modTime;
    @ApiModelProperty("文件创建时间")
    private String createAt;
    @ApiModelProperty("文件数据Hash")
    private String dataHash;
    @ApiModelProperty("文件密级（启用密级情况）")
    private String sc;
    @ApiModelProperty("文件大小")
    private Integer size;
    @ApiModelProperty("当前版本编号")
    private Integer verId;
    @ApiModelProperty("是否在回收站中")
    private Boolean inRecycle;
    @ApiModelProperty("=0，没有归档，=1文件已经归档")
    private Integer lock;
    @ApiModelProperty("父文件ID")
    private String fatherId;
    @ApiModelProperty("上传人")
    private String uploader;
    @ApiModelProperty("文件描述")
    private String desc;
    @ApiModelProperty("关键词/标签")
    private String keywords;
}
