package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserInfoVo implements Serializable {
    private String uid;
    private String name;
    private String nick;
    private String outUid;
    private String email;
    private String mobile;
    private String remark;
    @ApiModelProperty("0 正常，1 待审核的，2禁用的")
    private Integer status;
    @ApiModelProperty("4 系统增加，5 接口导入，6 AD/LDAP，7 企业微信，8 钉钉导入，10 批量导入，11 其他系统导入")
    private Integer from;
    @ApiModelProperty("0非密， 1内部， 2 秘密，3 机密，4绝密")
    private Integer sc;
    private String createAt;
    @ApiModelProperty("最后修改时间")
    private String lastTime;
    private String expires;
    @ApiModelProperty("所属组ID数组，表示根节点")
    private List<String> belongTo;
}
