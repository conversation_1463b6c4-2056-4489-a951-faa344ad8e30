package ltd.zstech.standard.util;

import lombok.extern.slf4j.Slf4j;
import ltd.zstech.standard.entity.TokenResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
public class MultiUserTokenManager {
    private final Map<String, UserToken> tokenStore = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final DuokeAPI duokeAPI;

    // 配置参数
    private static final long TOKEN_EXPIRY = 7200 * 1000; // 7200秒→毫秒
    private static final long REFRESH_MARGIN = 300 * 1000;   // 提前5分钟刷新
    private static final int MAX_RETRY = 3;

    @Autowired
    public MultiUserTokenManager(DuokeAPI duokeAPI) {
        this.duokeAPI = duokeAPI;
        startScheduledRefresh();
    }

    // 定时刷新任务（全局扫描）
    private void startScheduledRefresh() {
        scheduler.scheduleAtFixedRate(() -> {
            for (UserToken token : tokenStore.values()) {
                if (token.needRefresh()) {
                    CompletableFuture.runAsync(token::refresh);
                }
            }
        }, 1, 10, TimeUnit.MINUTES); // 每分钟扫描一次
    }

    // 获取用户Token（线程安全）
    public String getAccessToken(String username, String password) {
        UserToken userToken = tokenStore.computeIfAbsent(
                username,
                k -> new UserToken(username, password)
        );

        try {
            return userToken.getValidToken();
        } catch (Exception e) {
            tokenStore.remove(username); // 清理无效记录
            throw new RuntimeException("获取访问令牌失败，请检查凭证", e);
        }
    }

    private class UserToken {
        private final String username;
        private final String password;
        private final ReentrantLock lock = new ReentrantLock();

        private String accessToken;
        private long expiresAt;
        private volatile boolean initialized = false;
        private volatile boolean initializationFailed = false;
        private int refreshFailCount = 0;

        UserToken(String username, String password) {
            this.username = username;
            this.password = password;
            initializeToken(); // 首次初始化
        }

        // 获取有效Token（双重检查锁）
        String getValidToken() {
            if (!initialized && !initializationFailed) {
                initializeToken();
            }
            if (initializationFailed) {
                throw new RuntimeException("Token初始化失败");
            }
            return accessToken;
        }

        // 初始化Token（账号密码方式）
        private void initializeToken() {
            lock.lock();
            try {
                if (initialized || initializationFailed) return;
                while (refreshFailCount <= 3) {
                    try {
                        TokenResponse response = duokeAPI.getToken(username, password);
                        updateToken(response);
                        initialized = true;
                        log.info("[{}] Token初始化成功", username);
                        return;
                    } catch (Exception e) {
                        refreshFailCount++;
                        if (refreshFailCount > 3) {
                            initializationFailed = true;
                            log.error("[{}] Token初始化最终失败，已重试{}次",
                                    username, 3, e);
                            throw new RuntimeException("Token初始化失败");
                        }

                        try {
                            Thread.sleep(2 * 3);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("初始化被中断");
                        }
                    }
                }
            } finally {
                lock.unlock();
            }
        }

        // 刷新Token（使用当前Token）
        void refresh() {
            lock.lock();
            try {
                TokenResponse response = duokeAPI.refreshToken(accessToken);
                updateToken(response);
                log.debug("[{}] Token刷新成功", username);
            } catch (Exception e) {
                log.error("[{}] Token刷新失败，尝试重新初始化", username, e);
                initialized = false;
                initializeToken(); // 降级为重新初始化
            } finally {
                lock.unlock();
            }
        }

        // 更新Token状态
        private void updateToken(TokenResponse response) {
            this.accessToken = response.getAccessToken();
            this.expiresAt = System.currentTimeMillis() + (response.getExpiresIn() * 1000);
        }

        // 判断是否需要刷新
        boolean needRefresh() {
            return System.currentTimeMillis() > (expiresAt - REFRESH_MARGIN);
        }

        // 错误处理（实际项目需扩展）
        private void handleError(Exception e) {
            System.err.printf("[%s] Token刷新异常: %s%n", username, e.getMessage());
        }
    }

    @PreDestroy
    public void shutdown() {
        scheduler.shutdownNow();
    }
}
