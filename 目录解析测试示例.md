# 目录解析功能测试示例

## 测试场景

基于您提供的文档目录页，以下是测试用例和预期结果：

### 输入文档目录页内容
```
目    次

前  言 ................................................................ II
1 范围 ................................................................ 1
2 规范性引用文件 ........................................................ 1
3 术语和定义 ........................................................... 1
4 总则 ................................................................ 1
5 职责 ................................................................ 1
6 管理内容及要求 ........................................................ 2
  6.1 设备分级 ........................................................ 2
  6.2 设备停役 ........................................................ 2
  6.3 设备复役 ........................................................ 3
  6.4 报告和记录 ...................................................... 3
附录 .................................................................. 4
附录 A （规范性附录） 设备停复役管理流程图 ............................. 4
附录 B （规范性附录） 【专】设备分级表 ................................. 5
附录 C （规范性附录） 设备停役申请单 ................................... 6
附录 D （规范性附录） 设备复役通知单 ................................... 7
```

### 预期解析结果

#### 1. 扁平目录列表 (flatToc)
```json
[
  {
    "title": "前言",
    "level": 1,
    "pageNumber": "II",
    "index": 1,
    "originalText": "前  言 ................................................................ II"
  },
  {
    "title": "1 范围",
    "level": 1,
    "pageNumber": "1",
    "index": 2,
    "originalText": "1 范围 ................................................................ 1"
  },
  {
    "title": "2 规范性引用文件",
    "level": 1,
    "pageNumber": "1",
    "index": 3,
    "originalText": "2 规范性引用文件 ........................................................ 1"
  },
  {
    "title": "6 管理内容及要求",
    "level": 1,
    "pageNumber": "2",
    "index": 7,
    "originalText": "6 管理内容及要求 ........................................................ 2"
  },
  {
    "title": "6.1 设备分级",
    "level": 2,
    "pageNumber": "2",
    "index": 8,
    "originalText": "  6.1 设备分级 ........................................................ 2"
  },
  {
    "title": "6.2 设备停役",
    "level": 2,
    "pageNumber": "2",
    "index": 9,
    "originalText": "  6.2 设备停役 ........................................................ 2"
  },
  {
    "title": "附录",
    "level": 1,
    "pageNumber": "4",
    "index": 12,
    "originalText": "附录 .................................................................. 4"
  }
]
```

#### 2. 层级目录结构 (hierarchicalToc)
```json
[
  {
    "title": "前言",
    "level": 1,
    "pageNumber": "II",
    "children": []
  },
  {
    "title": "1 范围",
    "level": 1,
    "pageNumber": "1",
    "children": []
  },
  {
    "title": "6 管理内容及要求",
    "level": 1,
    "pageNumber": "2",
    "children": [
      {
        "title": "6.1 设备分级",
        "level": 2,
        "pageNumber": "2",
        "children": []
      },
      {
        "title": "6.2 设备停役",
        "level": 2,
        "pageNumber": "2",
        "children": []
      },
      {
        "title": "6.3 设备复役",
        "level": 2,
        "pageNumber": "3",
        "children": []
      },
      {
        "title": "6.4 报告和记录",
        "level": 2,
        "pageNumber": "3",
        "children": []
      }
    ]
  },
  {
    "title": "附录",
    "level": 1,
    "pageNumber": "4",
    "children": []
  }
]
```

## 测试API调用

### 1. 使用Postman测试

**请求URL**: `POST /fileOperation/getTableOfContents`

**请求体**:
```json
{
  "templateId": 123,
  "formdataId": 456
}
```

**预期响应**:
```json
{
  "success": true,
  "result": {
    "flatToc": [...],
    "hierarchicalToc": [...],
    "totalItems": 12
  },
  "code": 200,
  "message": "操作成功",
  "timestamp": 1703123456789
}
```

### 2. 使用curl测试

```bash
curl -X POST http://localhost:8080/fileOperation/getTableOfContents \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": 123,
    "formdataId": 456
  }'
```

## 功能验证要点

### 1. 域代码过滤测试
确保以下Word域代码被正确过滤：
- `HYPERLINK \l "_Toc21357"`
- `PAGEREF _Toc21357 \h`
- `MERGEFORMAT`

**测试方法**: 检查返回的title字段中不包含这些代码

### 2. 层级识别测试
验证系统能正确识别：
- **一级目录**: 数字开头的章节 (1, 2, 3...)
- **二级目录**: 带缩进的子章节 (6.1, 6.2...)
- **特殊章节**: 前言、附录等

### 3. 页码提取测试
验证能正确提取：
- **阿拉伯数字页码**: 1, 2, 3...
- **罗马数字页码**: I, II, III...

### 4. 文本清理测试
验证能正确清理：
- **点线连接符**: `....`, `…………`
- **多余空格**: 连续空格被合并为单个空格
- **特殊字符**: 域代码相关的特殊字符

## 错误处理测试

### 1. 无目录页文档
**测试场景**: 上传不包含目录页的文档
**预期结果**: 
```json
{
  "flatToc": [],
  "hierarchicalToc": [],
  "totalItems": 0
}
```

### 2. 格式异常文档
**测试场景**: 上传目录格式不规范的文档
**预期结果**: 系统应能容错处理，尽量解析可识别的目录项

### 3. 文件格式错误
**测试场景**: 上传非.docx格式文件
**预期结果**: 返回错误信息

## 性能测试

### 1. 大文档测试
- **文档大小**: 50MB以上
- **目录项数量**: 100+个目录项
- **预期响应时间**: < 10秒

### 2. 并发测试
- **并发请求数**: 10个同时请求
- **预期**: 所有请求都能正常处理

## 日志验证

检查应用日志中是否包含以下信息：
```
[INFO] 找到目录页，共 XX 个段落
[INFO] 解析目录项: 级别1 - 前言 (页码: II)
[INFO] 解析目录项: 级别2 - 6.1 设备分级 (页码: 2)
[INFO] 目录解析完成，共找到 XX 个目录项
```

## 集成测试

### 1. 与文档拆分功能集成
测试 `/fileOperation/splitWithTOC` 接口，验证：
- 文档拆分正常进行
- 目录信息正确提取
- 返回数据包含完整的拆分结果和目录结构

### 2. 前端集成测试
验证前端能正确：
- 发送请求到后端API
- 解析返回的JSON数据
- 显示扁平和层级目录结构
- 处理错误情况

## 回归测试

确保新功能不影响现有功能：
1. 原有的 `splitDocumentByHeadings` 方法仍然正常工作
2. 文档合并功能不受影响
3. 其他文档处理功能正常

通过以上测试用例，可以全面验证目录解析功能的正确性、稳定性和性能。
