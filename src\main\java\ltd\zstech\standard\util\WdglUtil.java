package ltd.zstech.standard.util;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 多可文档管理相关操作
 */
@Slf4j
@Component
public class WdglUtil {
    @Value("${jeecg.wdglUrl}")
    private String wdglUrl;
    @Value("${jeecg.wdglPass}")
    private String wdglPass;

    /**
     * 获取接口请求所需校验的hash
     *
     * @return
     */
    public String getHash() {
        String res = HttpUtil.get(wdglUrl + "/orgInterface?opr=getHash&p=" + wdglPass);
        log.info("获取的Hash为：{}", res);
        return res;
    }

    /**
     * 删除Hash
     *
     * @return
     */
    public boolean delHash(String hash) {
        String s = HttpUtil.get(wdglUrl + "/orgInterface?opr=delHash&hash=" + hash);
        if (s.equals("1")) {
            log.info("hash删除成功");
            return true;
        }
        return false;
    }

    /**
     * 添加组织
     *
     * @return
     */
    public String addGroup(String fatherId, String tempGroupId, String groupName, String groupDesc) {
        String hash = getHash();
        String res = "";
        try {
            // 给groupName和groupDesc进行URL编码
            groupName = URLEncoder.encode(groupName, StandardCharsets.UTF_8.toString());
            groupDesc = URLEncoder.encode(groupDesc, StandardCharsets.UTF_8.toString());
            String url = wdglUrl + "/orgInterface?opr=addGroup&hash=" + hash +
                    "&groupname=" + groupName +
                    "&groupdesc=" + groupDesc;
            if (StringUtils.isEmpty(fatherId)) {
                fatherId = "0";
                url += "&fatherid=" + fatherId;
            }
            if (StringUtils.isNotEmpty(tempGroupId)) {
                url += "&tempgroupid=" + tempGroupId;
            }
            log.info("添加组织请求的URL为：{}", url);
            res = HttpUtil.get(url);
        } catch (UnsupportedEncodingException e) {
            log.error("错误信息：{}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            delHash(hash);
        }
        return res;
    }

    /**
     * 删除组
     *
     * @param groupId
     * @return
     */
    public String delGroup(String groupId) {
        String hash = getHash();
        String res = "";
        try {
            res = HttpUtil.get(wdglUrl + "/orgInterface?opr=delGroup&hash=" + hash + "&groupid=" + groupId);
            log.info("删除组织返回结果：{}", res);
        } catch (Exception e) {
            log.error("错误信息：{}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            delHash(hash);
        }
        return res;
    }

    /**
     * 增加用户
     *
     * @param nickName
     * @param alias
     * @param password
     * @return
     */
    public String addUser(String nickName, String alias, String password) {
        String hash = getHash();
        String res = "";
        try {
            nickName = URLEncoder.encode(nickName, StandardCharsets.UTF_8.toString());
            alias = URLEncoder.encode(alias, StandardCharsets.UTF_8.toString());

        } catch (UnsupportedEncodingException e) {
            log.error("错误信息：{}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            delHash(hash);
        }
        return res;
    }


}
