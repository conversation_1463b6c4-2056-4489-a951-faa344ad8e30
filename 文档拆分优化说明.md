# 文档拆分服务优化说明

## 优化概述

原有的文档拆分服务主要依赖于文档中的标题样式（`isHeadingLevel()`方法）进行拆分，这种方式在某些情况下可能不够准确。现在新增了基于目录页中目录项进行拆分的优化方法，提高了拆分的准确性和可靠性。

## 新增功能

### 1. 新增方法：`splitDocumentByTOCItems()`

**功能描述**：根据文档目录页中的目录项进行文档拆分

**工作原理**：
1. 首先从文档的目录页提取所有目录项
2. 根据目录项的标题在文档正文中查找对应的段落
3. 按照目录项的顺序进行文档内容拆分
4. 如果目录页解析失败，自动回退到原有的基于标题样式的拆分方式

**优势**：
- 更准确：直接基于文档作者定义的目录结构
- 更可靠：避免了样式识别的不确定性
- 更智能：支持多种标题格式的匹配
- 容错性强：失败时自动回退到原有方法

### 2. 新增API接口：`/fileOperation/splitByTOCItems`

**请求方式**：POST

**请求参数**：
```json
{
  "templateId": 123,
  "formdataId": 456,
  "subTemplateId": 789
}
```

**响应结果**：
```json
{
  "success": true,
  "result": {
    "message": "基于目录项的文档拆分完成",
    "splitDocuments": [
      {
        "fileName": "第一章总则-0",
        "filePath": "123/456/第一章总则-0.docx"
      }
    ],
    "tableOfContents": [
      {
        "title": "第一章 总则",
        "level": 1,
        "pageNumber": "1",
        "index": 15
      }
    ],
    "totalSections": 5,
    "splitMethod": "TOC_ITEMS"
  }
}
```

## 核心算法优化

### 1. 智能标题匹配算法

新增的 `findParagraphByTocTitle()` 方法实现了多层次的标题匹配：

1. **精确匹配**：清理后的目录标题与段落文本完全一致
2. **包含匹配**：目录标题包含在段落文本中
3. **反向包含匹配**：段落文本包含在目录标题中
4. **模糊匹配**：去除空格和标点符号后进行比较

### 2. 标题清理算法

`cleanTitleForMatching()` 方法能够处理多种编号格式：

- 数字编号：`1.`、`1.1.`、`1.1.1.`
- 章节编号：`第一章`、`第二节`
- 中文编号：`一、`、`二、`
- 括号编号：`(一)`、`（1）`
- 页码信息：`...页码`、`……页码`

### 3. 目录页内容过滤

`isInTableOfContentsPage()` 方法能够识别并跳过目录页内容，避免将目录页内容包含在拆分结果中。

## 使用建议

### 1. 优先使用新方法

对于包含标准目录页的文档，建议优先使用新的 `/splitByTOCItems` 接口，它能提供更准确的拆分结果。

### 2. 兼容性保证

原有的拆分方法仍然保留，确保向后兼容：
- `/split` - 原有的基于标题样式的拆分
- `/splitWithTOC` - 基于标题样式拆分并提取目录信息

### 3. 错误处理

新方法具有良好的容错机制：
- 如果目录页解析失败，自动回退到原有方法
- 如果目录项匹配失败，会记录详细的日志信息
- 支持部分匹配，即使某些目录项找不到对应段落也能继续处理

## 技术实现细节

### 1. 文档节点处理

- 使用 Aspose.Words 的 NodeCollection 遍历文档结构
- 区分段落（Paragraph）和表格（Table）节点
- 保持原有的样式和格式信息

### 2. 内容范围确定

- 根据目录项顺序确定每个段落的内容范围
- 自动处理首页内容（第一个目录项之前的内容）
- 跳过目录页内容，避免重复包含

### 3. 文档生成

- 复制原文档的所有样式信息
- 保持表格和其他复杂结构的完整性
- 生成独立的 .docx 文件

## 日志和调试

新增了详细的日志记录：
- 目录项提取过程
- 段落匹配过程
- 内容范围确定过程
- 错误和异常处理

可以通过日志级别 `DEBUG` 查看详细的匹配过程。

## 性能考虑

- 目录页解析只进行一次
- 段落匹配使用高效的字符串算法
- 内存使用优化，及时清理临时对象
- 支持大文档的处理

## 后续扩展

1. **支持更多文档格式**：可扩展支持 .doc、.pdf 等格式
2. **自定义匹配规则**：允许用户定义特殊的标题匹配规则
3. **批量处理**：支持一次处理多个文档
4. **预览功能**：在实际拆分前预览拆分结果

## 总结

通过这次优化，文档拆分服务的准确性和可靠性得到了显著提升。新的基于目录项的拆分方法能够更好地理解文档结构，提供更精确的拆分结果，同时保持了良好的向后兼容性和容错能力。
