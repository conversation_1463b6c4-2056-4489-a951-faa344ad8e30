package ltd.zstech.standard.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import ltd.zstech.common.api.vo.Result;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.modules.commonservice.service.ICustomFormTemplateService;
import ltd.zstech.modules.views.entity.FormCustomFormTemplate;
import ltd.zstech.standard.entity.FileOrFolderDTO;
import ltd.zstech.standard.entity.FileOrFolderVo;
import ltd.zstech.standard.request.CreateFolderResponse;
import ltd.zstech.standard.request.RequestParameter;
import ltd.zstech.standard.util.DuokeAPI;
import ltd.zstech.standard.util.MultiUserTokenManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/formTrigger")
public class FormTrigger {

    @Resource
    private FormOperationService formOperationService;
    @Resource
    private ICustomFormTemplateService customFormTemplateService;
    @Resource
    private DuokeAPI duokeAPI;
    @Resource
    private MultiUserTokenManager multiUserTokenManager;
    @Value("${jeecg.path.upload}")
    private String filePath;

    /**
     * 制度新增后置事件
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "/zdAddTrigger")
    public Result<?> zdAddTrigger(@RequestBody JSONObject jsonObject) throws Exception {
        String result = (String) jsonObject.get("requestParam");
        JSONObject resJson = jsonObject.getJSONObject("result");
        System.out.println("表单保存结果：" + resJson.toString());
        RequestParameter request = JSON.parseObject(result, RequestParameter.class);
        Integer formdataId = resJson.getJSONObject("data").getInteger("dataId");
        Integer templateId = request.getTemplateId();

        String mainData = request.getMainData();
        JSONObject mainDataJson = JSONObject.parseObject(mainData);
        // 判断添加的是否是文件夹
        String lx = mainDataJson.getString("Lx");
        Integer fjmlid = mainDataJson.getInteger("Fjmlid");
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        String newFid = "";
        if (lx.equals("Folder")) {  // 文件夹
            // 查询该文件夹是否已经创建，未创建时则先创建，已创建时则获取文件夹的fid
            String fatherFid = "";
            if (fjmlid != null) {
                Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(fjmlid));
                Object fid = formById.get("fid");
                if (ObjectUtil.isNotEmpty(fid)) {
                    fatherFid = fid.toString();
                }
            }
            String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
            FileOrFolderDTO fileOrFolderList = duokeAPI.getFileOrFolderList(accessToken, fatherFid);
            List<FileOrFolderVo> folders = fileOrFolderList.getFolders();
            for (FileOrFolderVo folder : folders) {
                if (folder.getName().equals(mainDataJson.getString("Mlmc"))) {
                    newFid = folder.getId();
                    break;
                }
            }
            if (StringUtils.isBlank(newFid)) {  // 文件夹未创建
                CreateFolderResponse createFolder = duokeAPI.addFolder(accessToken, fatherFid, mainDataJson.getString("Mlmc"), mainDataJson.getString("Mlmc"));
                newFid = createFolder.getId();
            }

            // 更新文件夹的fid
            Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
            formById.put("fid", newFid);
            formById.put("Zt", null);
            formById.remove("ID");
            formOperationService.update(formTemplate.getFormtablename(), "ID=" + formdataId, formById);
        }
        if (lx.equals("File")) {    // 文件
            // 直接调用多可的文件上传接口上传文件到多可
            String fatherFid = "";
            if (fjmlid != null) {
                Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(fjmlid));
                Object fid = formById.get("fid");
                if (ObjectUtil.isNotEmpty(fid)) {
                    fatherFid = fid.toString();
                }
                String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
                String attachments = mainDataJson.getString("WJ");
                Path path = Paths.get(filePath, attachments);
                File file = new File(path.toString());
                FileInputStream fis = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(), null, fis);
                String res = duokeAPI.uploadFile(accessToken, fatherFid, multipartFile);
                JSONObject object = JSONObject.parseObject(res);
                if (object.getIntValue("errcode") == 0) {
                    newFid = object.getString("fileId");
                }

                // 更新文件的fid
                if (StringUtils.isNoneBlank(newFid)) {
                    Map<String, Object> formFile = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
                    formFile.put("fid", newFid);
                    formFile.put("downloadUrl", duokeAPI.downloadFile(accessToken, newFid).get(0));
                    formFile.remove("ID");
                    formOperationService.update(formTemplate.getFormtablename(), "ID=" + formdataId, formFile);
                }
            }
        }
        return Result.success(true);
    }
}
