package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileOrFolderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件/文件夹ID")
    private String id;
    @ApiModelProperty("文件/文件夹名称")
    private String name;
    @ApiModelProperty("文件/文件夹大小")
    private Integer size;
    @ApiModelProperty("文件/文件夹修改时间")
    private String modTime;
    @ApiModelProperty("文件/文件夹创建时间")
    private String createAt;
    @ApiModelProperty("文件/文件夹数据Hash")
    private String dataHash;
    @ApiModelProperty("文件/文件夹类型,1表示文件，2表示文件夹")
    private Integer xdType;
    @ApiModelProperty("上传人")
    private String uploader;
}
