# 文档拆分服务API使用示例

## 1. 基于目录项的文档拆分（推荐）

### 接口地址
```
POST /fileOperation/splitByTOCItems
```

### 请求示例
```javascript
// 使用新的优化方法进行文档拆分
const splitByTOCItems = async (templateId, formdataId, subTemplateId) => {
  const response = await fetch('/fileOperation/splitByTOCItems', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId,
      subTemplateId: subTemplateId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('拆分方法:', result.result.splitMethod);
    console.log('拆分文档:', result.result.splitDocuments);
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总段落数:', result.result.totalSections);
    console.log('消息:', result.result.message);
  } else {
    console.error('拆分失败:', result.message);
  }
};

// 调用示例
splitByTOCItems(123, 456, 789);
```

### 响应示例
```json
{
  "success": true,
  "result": {
    "message": "基于目录项的文档拆分完成",
    "splitDocuments": [
      {
        "fileName": "首页-0",
        "filePath": "123/456/首页-0.docx"
      },
      {
        "fileName": "前言-1",
        "filePath": "123/456/前言-1.docx"
      },
      {
        "fileName": "范围-2",
        "filePath": "123/456/范围-2.docx"
      },
      {
        "fileName": "引用文件-3",
        "filePath": "123/456/引用文件-3.docx"
      },
      {
        "fileName": "术语和定义-4",
        "filePath": "123/456/术语和定义-4.docx"
      }
    ],
    "tableOfContents": [
      {
        "title": "前言",
        "level": 1,
        "pageNumber": "II",
        "index": 5,
        "originalText": "前言 ................................................................ II"
      },
      {
        "title": "范围",
        "level": 1,
        "pageNumber": "1",
        "index": 8,
        "originalText": "1 范围 ................................................................ 1"
      },
      {
        "title": "引用文件",
        "level": 1,
        "pageNumber": "1",
        "index": 9,
        "originalText": "2 引用文件 ................................................................ 1"
      },
      {
        "title": "术语和定义",
        "level": 1,
        "pageNumber": "2",
        "index": 10,
        "originalText": "3 术语和定义 ................................................................ 2"
      }
    ],
    "totalSections": 5,
    "splitMethod": "TOC_ITEMS"
  }
}
```

## 2. 比较不同拆分方法

### 接口地址
```
POST /fileOperation/compareSplitMethods
```

### 请求示例
```javascript
// 比较基于标题样式和基于目录项的两种拆分方法
const compareMethods = async (templateId, formdataId) => {
  const response = await fetch('/fileOperation/compareSplitMethods', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('基于标题样式的结果:', result.result.headingBasedResult);
    console.log('基于目录项的结果:', result.result.tocBasedResult);
    console.log('统计信息:', result.result.statistics);
    console.log('推荐方法:', result.result.recommendation);
  }
};

// 调用示例
compareMethods(123, 456);
```

### 响应示例
```json
{
  "success": true,
  "result": {
    "headingBasedResult": {
      "splitDocuments": [...],
      "tableOfContents": [...],
      "totalSections": 3
    },
    "tocBasedResult": {
      "splitDocuments": [...],
      "tableOfContents": [...],
      "totalSections": 5
    },
    "statistics": {
      "headingBasedSections": 3,
      "tocBasedSections": 5,
      "headingBasedTocItems": 3,
      "tocBasedTocItems": 5
    },
    "recommendation": "建议使用基于目录项的拆分方法"
  }
}
```

## 3. 获取文档目录信息

### 接口地址
```
POST /fileOperation/getTableOfContents
```

### 请求示例
```javascript
// 仅获取文档的目录信息，不进行拆分
const getTableOfContents = async (templateId, formdataId) => {
  const response = await fetch('/fileOperation/getTableOfContents', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('扁平目录:', result.result.flatToc);
    console.log('层级目录:', result.result.hierarchicalToc);
    console.log('目录项总数:', result.result.totalItems);
  }
};

// 调用示例
getTableOfContents(123, 456);
```

## 4. 传统拆分方法（兼容性）

### 基于标题样式的拆分（包含目录信息）
```javascript
// 原有的基于标题样式的拆分方法
const splitWithTOC = async (templateId, formdataId, subTemplateId) => {
  const response = await fetch('/fileOperation/splitWithTOC', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId,
      subTemplateId: subTemplateId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('拆分文档:', result.result.splitDocuments);
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总段落数:', result.result.totalSections);
  }
};
```

### 基于标题样式的拆分（仅拆分结果）
```javascript
// 最原始的拆分方法，只返回拆分后的文档列表
const split = async (templateId, formdataId, subTemplateId) => {
  const response = await fetch('/fileOperation/split', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId,
      subTemplateId: subTemplateId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('拆分完成:', result.result);
  }
};
```

## 5. 错误处理示例

```javascript
const handleSplitDocument = async (templateId, formdataId, subTemplateId) => {
  try {
    // 优先尝试基于目录项的拆分
    const response = await fetch('/fileOperation/splitByTOCItems', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: templateId,
        formdataId: formdataId,
        subTemplateId: subTemplateId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      if (result.result.splitMethod === 'TOC_ITEMS') {
        console.log('使用基于目录项的拆分方法成功');
      } else {
        console.log('自动回退到基于标题样式的拆分方法');
      }
      return result.result;
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('文档拆分失败:', error.message);
    
    // 如果新方法失败，可以尝试传统方法
    try {
      const fallbackResponse = await fetch('/fileOperation/splitWithTOC', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateId: templateId,
          formdataId: formdataId,
          subTemplateId: subTemplateId
        })
      });
      
      const fallbackResult = await fallbackResponse.json();
      if (fallbackResult.success) {
        console.log('使用传统拆分方法成功');
        return fallbackResult.result;
      }
    } catch (fallbackError) {
      console.error('传统拆分方法也失败:', fallbackError.message);
    }
    
    throw error;
  }
};
```

## 6. 使用建议

1. **优先使用新方法**：对于包含标准目录页的文档，建议使用 `/splitByTOCItems` 接口
2. **比较结果**：可以使用 `/compareSplitMethods` 接口比较两种方法的效果
3. **错误处理**：新方法具有自动回退机制，但建议在客户端也实现降级处理
4. **日志监控**：关注服务端日志，了解拆分过程和可能的问题
5. **文档格式**：确保文档包含标准的目录页，格式为"目次"、"目录"或"TABLE OF CONTENTS"

## 7. 性能优化建议

1. **缓存结果**：对于相同的文档，可以缓存拆分结果
2. **异步处理**：对于大文档，建议使用异步处理
3. **批量操作**：如果需要处理多个文档，考虑批量API
4. **资源清理**：及时清理不需要的拆分文件
