package ltd.zstech.standard.util;

import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.io.resource.MultiResource;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.standard.entity.*;
import ltd.zstech.standard.request.CreateFolderResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Service
public class DuokeAPI {
    @Value("${jeecg.wdglUrl}")
    private String wdglUrl;

    private static final String GET_TOKEN = "/api/ou/getToken";
    private static final String REFRESH_TOKEN = "/api/ou/tokenDefer";
    private static final String GET_GROUP_LIST = "/api/ou/group/list";
    private static final String ADD_GROUP = "/api/ou/group/add";
    private static final String UPDATE_GROUP = "/api/ou/group/update";
    private static final String DELETE_GROUP = "/api/ou/group/delete";
    private static final String GET_GROUP_OR_USER_SIMPLE_INFO = "/api/ou/group/type";
    private static final String ADD_USER = "/api/ou/user/add";
    private static final String UPDATE_USER = "/api/ou/user/update";
    private static final String GET_USER_INFO = "/api/ou/user/detail";
    private static final String GET_USER_LIST_OF_GROUP = "/api/ou/group/users";
    private static final String ADD_MEMBERS_OF_GROUP = "/api/ou/group/addMembers";
    private static final String REMOVE_MEMBERS_OF_GROUP = "/api/ou/group/removeMembers";
    private static final String DISABLE_USER = "/api/ou/user/disable";
    private static final String ENABLE_USER = "/api/ou/user/enable";
    private static final String DELETE_USER = "/api/ou/user/delete";
    private static final String ADD_FOLDER = "/api/ff/folder/add";
    private static final String UPDATE_FOLDER = "/api/ff/folder/update";
    private static final String DELETE_FOLDER = "/api/ff/folder/delete";
    private static final String SET_FOLDER_PERMISSION = "/api/ff/folder/setPermission";
    private static final String GET_FOLDER_PERMISSION_LIST = "/api/ff/folder/permissionList";
    private static final String GET_USER_PERMISSION_OF_FOLDER = "/api/ff/folder/allPermission";
    private static final String GET_FILE_OR_FOLDER_LIST = "/api/ff/list";
    private static final String UPLOAD_FILE = "/api/ff/file/uploadUrl";
    private static final String GET_FILE_DETAIL = "/api/ff/file/detail";
    private static final String FILE_VIEW = "/api/ff/file/view";
    private static final String ONLINE_EDIT_FILE = "/api/ff/file/edit";
    private static final String DOWNLOAD_FILE = "/api/ff/file/download";
    private static final String DELETE_FILE = "/api/ff/file/delete";
    private static final String RENAME_FILE = "/api/ff/file/rename";
    private static final String GET_FILE_PUBLISH_INFO = "/api/ff/file/publish";
    private static final String SEARCH_FILE_BY_TITLE = "/api/ff/file/titleSearch";

    /**
     * 根据账号密码获取token
     *
     * @param username
     * @param password
     * @return
     */
    public TokenResponse getToken(String username, String password) {
        JSONObject params = new JSONObject();
        params.put("name", username);
        params.put("password", password);
        String urlString = wdglUrl + GET_TOKEN;
        log.info("请求的接口地址：{}", urlString);
        String res = HttpUtil.post(urlString, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        int errCode = obj.getIntValue("errcode");
        if (errCode == 0) {
            TokenResponse tokenResponse = new TokenResponse();
            tokenResponse.setAccessToken(obj.getString("accessToken"));
            tokenResponse.setExpiresIn(obj.getIntValue("expireIn"));
            return tokenResponse;
        }
        return null;
    }

    /**
     * 刷新token
     *
     * @param currentToken
     * @return
     */
    public TokenResponse refreshToken(String currentToken) {
        String urlString = wdglUrl + REFRESH_TOKEN + "?token=" + currentToken;
        String res = HttpRequest.get(urlString)
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .execute()
                .body();
        JSONObject obj = JSONObject.parseObject(res);
        int errCode = obj.getIntValue("errcode");
        if (errCode == 0) {
            TokenResponse tokenResponse = new TokenResponse();
            tokenResponse.setAccessToken(obj.getString("accessToken"));
            tokenResponse.setExpiresIn(obj.getIntValue("expireIn"));
            return tokenResponse;
        }
        return null;
    }

    /**
     * 获取组织结构的子组列表
     *
     * @param fatherUid
     * @param accessToken
     * @return 权限要求：需要目标组的【组查看】权限，没有权限返回错误信息。
     */
    public List<GroupVo> getGroupList(String fatherUid, String accessToken, boolean iter) {
        String urlString = wdglUrl + GET_GROUP_LIST + "?iter=" + iter + "&token=" + accessToken + "&fatherUid=" + fatherUid;
        // if (StringUtils.isNotEmpty(fatherUid)) {
        //     urlString = urlString + "&fatherUid=" + fatherUid;
        // }
        String res = HttpUtil.get(urlString);
        JSONObject obj = JSONObject.parseObject(res);
        int errCode = obj.getIntValue("errcode");
        if (errCode == 0) {
            return parseGroups(obj.getJSONArray("members"));
        }
        return null;
    }

    private List<GroupVo> parseGroups(JSONArray membersArray) {
        if (membersArray == null) {
            return new ArrayList<>();
        }
        List<GroupVo> groupList = new ArrayList<>();
        for (Object memberObj : membersArray) {
            JSONObject groupJson = (JSONObject) memberObj;
            GroupVo groupVo = groupJson.toJavaObject(GroupVo.class);
            if (groupJson.containsKey("members") && groupJson.getJSONArray("members") != null && !groupJson.getJSONArray("members").isEmpty()) {
                groupVo.setMembers(parseGroups(groupJson.getJSONArray("members")));
            }
            groupList.add(groupVo);
        }
        return groupList;
    }


    /**
     * 新增组
     *
     * @param fatherUid
     * @param accessToken
     * @param name
     * @param remark
     * @return 权限要求：需要目标组的【组增加删除修改】权限。
     */
    public Boolean addGroup(String fatherUid, String accessToken, String name, String remark) {
        List<GroupVo> groupList = getGroupList(fatherUid, accessToken, true);
        // 根据fatherUid获取fatherUid下子节点中的orderOfFather最大值
        int maxOrderOfFather = groupList.stream().mapToInt(GroupVo::getOrderOfFather).max().orElse(0);
        JSONObject params = new JSONObject();
        params.put("name", name);
        params.put("remark", remark);
        params.put("orderOfFather", maxOrderOfFather + 1);
        String urlString = wdglUrl + ADD_GROUP + "?fatherUid=" + fatherUid + "&token=" + accessToken;
        String res = HttpUtil.post(urlString, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        int errCode = obj.getIntValue("errcode");
        return errCode == 0;
    }

    /**
     * 更新组
     *
     * @param accessToken
     * @param uid
     * @param name
     * @param remark
     * @return 权限要求：需要目标组的【组增加删除修改】权限。
     */
    public boolean editGroup(String accessToken, String uid, String name, String remark) {
        List<GroupVo> groupList = getGroupList("", accessToken, true);
        // 根据uid从列表中递归去查找
        GroupVo group = findGroupByUid(groupList, uid);

        String urlString = wdglUrl + UPDATE_GROUP + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("uid", uid);
        params.put("name", name);
        params.put("remark", remark);
        params.put("orderOfFather", Objects.requireNonNull(group).getOrderOfFather());
        String res = HttpUtil.post(urlString, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        int errCode = obj.getIntValue("errcode");
        return errCode == 0;
    }

    private GroupVo findGroupByUid(List<GroupVo> groupList, String uid) {
        // groupList中，每个groupVo包含members子集，需要递归查询uid对应的数据
        for (GroupVo groupVo : groupList) {
            if (groupVo.getUid().equals(uid)) {
                return groupVo;
            } else {
                GroupVo member = findGroupByUid(groupVo.getMembers(), uid);
                if (member != null) {
                    return member;
                }
            }
        }
        return null;
    }

    /**
     * 删除组
     *
     * @param accessToken
     * @param uid
     * @return 权限要求：需要目标组的【组增加删除修改】权限。
     */
    public boolean deleteGroup(String accessToken, String uid) {
        String url = wdglUrl + DELETE_GROUP + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0;
    }

    /**
     * 根据ID得到组或者用户简单信息
     *
     * @param accessToken
     * @param uid         组ID或者用户ID
     * @return type=1 为用户， =2 为组
     * 权限要求：需要有此用户所在组的【用户查看及搜索】权限或者当前用户属于子组
     */
    public String getGroupOrUserSimpleInfo(String accessToken, String uid) {
        String url = wdglUrl + GET_GROUP_OR_USER_SIMPLE_INFO + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0 ? obj.getString("name") : null;
    }

    /**
     * 新增用户
     *
     * @param fuid
     * @param accessToken
     * @param name
     * @param nick
     * @param secretKey
     * @param email
     * @param mobile
     * @param remark
     * @return 权限要求：需要fuid组的【组用户增删改】权限
     */
    public boolean addUser(String fuid, String accessToken, String name, String nick, String secretKey, String email, String mobile, String remark) {
        String url = wdglUrl + ADD_USER + "?token=" + accessToken + "&fuid=" + fuid;
        JSONObject params = new JSONObject();
        params.put("name", name);
        params.put("nick", nick);
        params.put("secretKey", secretKey);
        params.put("email", email);
        params.put("mobile", mobile);
        params.put("remark", remark);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0;
    }

    /**
     * 更新用户基本信息
     *
     * @param accessToken
     * @param uid
     * @param name
     * @param nick
     * @param email
     * @param mobile
     * @param remark
     * @return 权限要求：需要有此用户所在组的【组用户增删改】权限
     */
    public boolean updateUser(String accessToken, String uid, String name, String nick, String email, String mobile, String remark) {
        String url = wdglUrl + UPDATE_USER + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("uid", uid);
        params.put("name", name);
        params.put("nick", nick);
        params.put("email", email);
        params.put("mobile", mobile);
        params.put("remark", remark);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0;
    }

    /**
     * 得到用户详细信息和所在的组
     *
     * @param uid
     * @param accessToken
     * @return 权限要求：需要有此用户所在组的【用户查看及搜索】权限
     */
    public UserInfoVo getUserInfo(String uid, String accessToken) {
        String url = wdglUrl + GET_USER_INFO + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return JSONObject.parseObject(obj.getString("user"), UserInfoVo.class);
        }
        return null;
    }

    /**
     * 得到一个组中的用户成员ID，名称和权限
     *
     * @param fatherUid
     * @param accessToken
     * @return 权限要求：需要有此用户所在组的【用户查看】权限
     */
    public List<UserOfGroupVo> getUserListOfGroup(String fatherUid, String accessToken) {
        String url = wdglUrl + GET_USER_LIST_OF_GROUP + "?token=" + accessToken + "&fatherUid=" + fatherUid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return obj.getJSONArray("users").toJavaList(UserOfGroupVo.class);
        }
        return null;
    }

    /**
     * 把多个用户增加到组成员中，同时设置权限
     *
     * @param accessToken
     * @param fatherUid   父组ID，如果为空，增加到根节点
     * @param memberUids  多个用户ID，中间用英文逗号连接
     * @param ps          多个权限，中间用英文逗号连接
     * @param deadline    授权截至时间，为空表示授权永久有效
     * @return 权限要求：需要有此用户所在组的【组/用户授权】权限
     */
    public boolean addMembersOfGroup(String accessToken, String fatherUid, String memberUids, String ps, String deadline) {
        String url = wdglUrl + ADD_MEMBERS_OF_GROUP + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        if (StringUtils.isNotEmpty(fatherUid)) {
            params.put("fatherUid", fatherUid);
        }
        params.put("memberUids", memberUids);
        params.put("ps", ps);
        params.put("deadline", deadline);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 删除组中的用户授权
     *
     * @param accessToken
     * @param fatherUid
     * @param memberUids
     * @return 权限要求：
     */
    public boolean removeMembersOfGroup(String accessToken, String fatherUid, String memberUids) {
        String url = wdglUrl + REMOVE_MEMBERS_OF_GROUP + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("fatherUid", fatherUid);
        params.put("memberUids", memberUids);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 禁用用户
     *
     * @param accessToken
     * @param uid
     * @return 权限要求：需要有此用户所在组的【组用户增删改】权限
     */
    public boolean disableUser(String accessToken, String uid) {
        String url = wdglUrl + DISABLE_USER + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 启用用户
     *
     * @param accessToken
     * @param uid
     * @return 权限要求：需要有此用户所在组的【组用户增删改】权限
     */
    public boolean enableUser(String accessToken, String uid) {
        String url = wdglUrl + ENABLE_USER + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 删除用户
     *
     * @param accessToken
     * @param uid
     * @return 权限要求：需要有此用户所在组的【组用户增删改】权限
     */
    public boolean deleteUser(String accessToken, String uid) {
        String url = wdglUrl + DELETE_USER + "?token=" + accessToken + "&uid=" + uid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 创建文件夹
     *
     * @param accessToken
     * @param fid
     * @param name
     * @param desc
     * @return 权限要求：需要目标文件夹的【文件夹新建/修改】权限
     */
    public CreateFolderResponse addFolder(String accessToken, String fid, String name, String desc) {
        String url = wdglUrl + ADD_FOLDER + "?token=" + accessToken + "&fid=" + fid;
        JSONObject params = new JSONObject();
        params.put("name", name);
        params.put("desc", desc);
        params.put("order", 0);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            CreateFolderResponse response = new CreateFolderResponse();
            response.setId(obj.getString("id"));
            response.setName(obj.getString("name"));
            response.setDesc(desc);
            return response;
        }
        return null;
    }



    /**
     * 修改文件夹名称/描述
     *
     * @param accessToken
     * @param id
     * @param name
     * @param desc
     * @return 权限要求：需要目标文件夹的【文件夹新建/修改】权限
     */
    public boolean updateFolder(String accessToken, String id, String name, String desc) {
        String url = wdglUrl + UPDATE_FOLDER + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("id", id);
        params.put("name", name);
        params.put("desc", desc);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 删除文件夹
     *
     * @param accessToken
     * @param fid
     * @return 权限要求：需要源文件夹【文件夹删除】权限
     */
    public boolean deleteFolder(String accessToken, String fid) {
        String url = wdglUrl + DELETE_FOLDER + "?token=" + accessToken + "&fid=" + fid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }

    /**
     * 设置文件夹权限
     *
     * @param accessToken
     * @param id
     * @param ouids
     * @param ps
     * @param deadline
     * @return 权限要求：需要文件夹【文件夹授权】权限
     */
    public boolean setFolderPermission(String accessToken, String id, String ouids, String ps, String deadline) {
        String url = wdglUrl + SET_FOLDER_PERMISSION + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("id", id);
        params.put("ouids", ouids);
        params.put("ps", ps);
        params.put("deadline", deadline);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getInteger("errcode") == 0;
    }


    /**
     * 得到一个文件夹的授权信息，不包括继承权限
     *
     * @param accessToken
     * @param fid
     * @return 权限要求：需要文件夹【文件夹授权】权限
     */
    public List<FolderPermissionVo> getFolderPermissionList(String accessToken, String fid) {
        String url = wdglUrl + GET_FOLDER_PERMISSION_LIST + "?token=" + accessToken + "&fid=" + fid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return obj.getJSONArray("psCells").toJavaList(FolderPermissionVo.class);
        }
        return null;
    }

    /**
     * 得到一个用户针对一个文件夹/文件的全部授权，包括继承权限
     *
     * @param accessToken
     * @param fid
     * @param uid
     * @return 权限要求：需要查询文件夹的【文件夹授权】权限
     */
    public List<String> getUserPermissionOfFolder(String accessToken, String fid, String uid) {
        String url = wdglUrl + GET_USER_PERMISSION_OF_FOLDER + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("fid", fid);
        params.put("uid", uid);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            String ps = obj.getString("ps");
            return Arrays.asList(ps.split(","));
        }
        return null;
    }

    /**
     * 得到一个文件夹下的子文件夹和文件基本信息，不递归
     *
     * @param accessToken
     * @param fid
     * @return
     */
    public FileOrFolderDTO getFileOrFolderList(String accessToken, String fid) {
        String url = wdglUrl + GET_FILE_OR_FOLDER_LIST + "?token=" + accessToken + "&fid=" + fid;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            List<FileOrFolderVo> files = obj.getJSONArray("files").toJavaList(FileOrFolderVo.class);
            List<FileOrFolderVo> temps = new CopyOnWriteArrayList<>(files);
            for (FileOrFolderVo file : temps) {
                FileDetailVo fileDetail = getFileDetail(accessToken, file.getId());
                file.setUploader(fileDetail.getUploader());
            }
            List<FileOrFolderVo> folders = obj.getJSONArray("folders").toJavaList(FileOrFolderVo.class);
            return new FileOrFolderDTO(temps, folders);
        }
        return null;
    }

    /**
     * 文件上传，分两步，第一步：获取上传地址，第二步：上传文件
     *
     * @param accessToken
     * @param fid
     * @param file
     * @return
     * @throws IOException
     */
    public String uploadFile(String accessToken, String fid, MultipartFile file) {
        // 获取上传文件的地址
        try {
            String urlStr = wdglUrl + UPLOAD_FILE + "?token=" + accessToken;
            JSONObject params = new JSONObject();
            params.put("fid", fid);
            log.info("请求参数： {}", params.toJSONString());
            String res = HttpUtil.post(urlStr, params.toJSONString());
            JSONObject obj = JSONObject.parseObject(res);
            if (obj.getIntValue("errcode") == 0) {
                String url = obj.getString("url");
                // 执行文件上传
                String uploadUrl = wdglUrl + url;
                log.info("上传文件地址： {}", uploadUrl);
                MultiResource multiResource = new MultiResource(new InputStreamResource(file.getInputStream(), file.getOriginalFilename()));
                String uploadfile = HttpRequest.post(uploadUrl)
                        .form("uploadfile", multiResource)
                        .execute().body();
                return uploadfile;
            }
        } catch (Exception e) {
            log.error("错误信息：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 得到一个文件的详细信息
     *
     * @param accessToken
     * @param id
     * @return 权限要求：需要【文件查看】权限
     */
    public FileDetailVo getFileDetail(String accessToken, String id) {
        String url = wdglUrl + GET_FILE_DETAIL + "?token=" + accessToken + "&id=" + id;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return obj.getJSONObject("xdoc").toJavaObject(FileDetailVo.class);
        }
        return null;
    }

    /**
     * 文件预览
     *
     * @param accessToken
     * @param id
     * @return 权限要求：需要【文件预览】权限
     */
    public String fileView(String accessToken, String id) {
        return wdglUrl + FILE_VIEW + "?token=" + accessToken + "&id=" + id;
    }

    /**
     * 文件在线修改，实现文件在线修改，支持office文件多人在线协同编辑，此功能需要系统安装onlyoffice,修改完成关闭页面后，系统会自动生成新版本文件，原来文件保存为历史版本。如果文件所在文件夹设置了修改审核，审核后修改版本才能生效
     *
     * @param accessToken
     * @param id
     * @return 权限要求：需要【文件修改】权限
     */
    public String onlineEditFile(String accessToken, String id) {
        return wdglUrl + ONLINE_EDIT_FILE + "?token=" + accessToken + "&id=" + id;
    }

    /**
     * 文件下载
     *
     * @param accessToken
     * @param ids
     * @return 权限要求：需要【文件下载】权限
     */
    public List<String> downloadFile(String accessToken, String ids) {
        List<String> downloadUrls = new ArrayList<>();
        List<String> fileIds = new ArrayList<>();
        if (ids.contains(",")) {
            String[] idsArr = ids.trim().split(",");
            fileIds.addAll(Arrays.asList(idsArr));
        } else {
            fileIds.add(ids);
        }
        for (String id : fileIds) {
            String url = wdglUrl + DOWNLOAD_FILE + "?token=" + accessToken + "&id=" + id;
            downloadUrls.add(url);
        }
        return downloadUrls;
    }

    /**
     * 文件删除
     *
     * @param accessToken
     * @param ids
     * @return 权限要求：需要【文件删除】权限
     */
    public boolean deleteFile(String accessToken, String ids) {
        String url = wdglUrl + DELETE_FILE + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("ids", ids);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0;
    }

    /**
     * 重命名文件
     *
     * @param accessToken
     * @param id
     * @param name
     * @return 权限要求：需要【文件上传/修改】权限
     */
    public boolean renameFile(String accessToken, String id, String name) {
        String url = wdglUrl + RENAME_FILE + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("id", id);
        params.put("name", name);
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        return obj.getIntValue("errcode") == 0;
    }

    /**
     * 得到一个文件的发布信息，包括文件发布预览和下载地址，发布密码，有效期
     *
     * @param accessToken
     * @param id
     * @return 权限要求：需要【文件发布】权限
     */
    public FilePublishInfoVo getFilePublishInfo(String accessToken, String id) {
        String url = wdglUrl + GET_FILE_PUBLISH_INFO + "?token=" + accessToken + "&id=" + id;
        String res = HttpUtil.get(url);
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return JSONObject.parseObject(obj.getString("pubInfo"), FilePublishInfoVo.class);
        }
        return null;
    }

    /**
     * 文件标题搜索，此方法速度由搜索范围决定，请根据事情实际搜索时间调整参数
     *
     * @param accessToken
     * @param fid
     * @param key
     * @return 权限要求：搜索结果文件如果有【文件基本信息查看】权限才能显示，没有权限不会出现在结果中
     */
    public List<FileDetailVo> searchFileByTitle(String accessToken, String fid, String key) {
        String url = wdglUrl + SEARCH_FILE_BY_TITLE + "?token=" + accessToken;
        JSONObject params = new JSONObject();
        params.put("fid", fid);
        params.put("key", key);
        params.put("style", 2); //搜索匹配方式，=1 前匹配，=2 部分匹配
        params.put("maxCount", 1024);
        params.put("iter", false); //=true 包括子文件夹（递归），=false 不递归
        String res = HttpUtil.post(url, params.toJSONString());
        JSONObject obj = JSONObject.parseObject(res);
        if (obj.getIntValue("errcode") == 0) {
            return obj.getJSONArray("items").toJavaList(FileDetailVo.class);
        }
        return null;
    }




}
