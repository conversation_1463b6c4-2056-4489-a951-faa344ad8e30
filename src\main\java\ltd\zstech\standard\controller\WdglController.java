package ltd.zstech.standard.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.common.api.vo.Result;
import ltd.zstech.common.aspect.annotation.AutoLog;
import ltd.zstech.standard.entity.*;
import ltd.zstech.standard.request.*;
import ltd.zstech.standard.util.DuokeAPI;
import ltd.zstech.standard.util.MultiUserTokenManager;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/wdgl")
public class WdglController {
    @Resource
    private MultiUserTokenManager multiUserTokenManager;
    @Resource
    private DuokeAPI duokeAPI;

    @GetMapping("/getAccessToken")
    public Result<?> getHash() {
        String accessToken = duokeAPI.getToken("test", "fb157e5d5fb66f620e4268bc6d9c878e").getAccessToken();
        return Result.ok(accessToken);
    }

    /**
     * @param request
     * @return
     */
    @PostMapping(value = "/getFileOrFolderList")
    @AutoLog
    @ApiOperation(value = "得到一个文件夹下的子文件夹和文件基本信息，不递归，父文件夹ID，如果为空得到根节点文件夹")
    public Result<FileOrFolderDTO> getFileOrFolderList(@RequestBody FidRequest request) {
        String fid = request.getFid();
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        FileOrFolderDTO fileOrFolderDTO = duokeAPI.getFileOrFolderList(accessToken, fid);
        return Result.success(fileOrFolderDTO);
    }

    @PostMapping("/createFolder")
    @ApiOperation(value = "创建文件夹")
    @AutoLog
    public Result<CreateFolderResponse> createFolder(@RequestBody CreateFolderRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String fid = request.getFid();
        CreateFolderResponse result = duokeAPI.addFolder(accessToken, fid, request.getName(), request.getDesc());
        return Result.success(result);
    }

    @PostMapping("/uploadFile")
    @ApiOperation(value = "上传文件")
    @AutoLog
    public Result<?> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam(value = "fid") String fid) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String res = duokeAPI.uploadFile(accessToken, fid, file);
        return Result.ok(res);
    }

    @PostMapping(value = "/fileView")
    @ApiOperation(value = "文件预览")
    @AutoLog
    public Result<?> fileView(@RequestBody IdRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String res = duokeAPI.fileView(accessToken, request.getId());
        return Result.ok(res);
    }

    @PostMapping(value = "/onlineEditFile")
    @ApiOperation(value = "在线编辑文件，需要安装OnlyOffice组件才能支持")
    @AutoLog
    public Result<?> onlineEditFile(@RequestBody IdRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String res = duokeAPI.onlineEditFile(accessToken, request.getId());
        return Result.ok(res);
    }


    @PostMapping(value = "/getFileDetail")
    @ApiOperation(value = "获取文件详情")
    @AutoLog
    public Result<FileDetailVo> getFileDetail(@RequestBody IdRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        FileDetailVo detail = duokeAPI.getFileDetail(accessToken, request.getId());
        return Result.success(detail);
    }

    @ApiOperation(value = "文件下载(获取下载的链接)")
    @AutoLog
    @PostMapping("/downloadFile")
    public Result<List<String>> downloadFile(@RequestBody IdsRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<String> downloadUrls = duokeAPI.downloadFile(accessToken, request.getIds());
        return Result.success(downloadUrls);
    }

    @ApiOperation(value = "文件删除")
    @AutoLog
    @PostMapping("/deleteFile")
    public Result<Boolean> deleteFile(@RequestBody IdsRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        boolean result = duokeAPI.deleteFile(accessToken, request.getIds());
        return Result.success(result);
    }

    @ApiOperation(value = "文件重命名")
    @AutoLog
    @PostMapping("/renameFile")
    public Result<Boolean> renameFile(@RequestBody FileRenameRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        boolean result = duokeAPI.renameFile(accessToken, request.getId(), request.getName());
        return Result.success(result);
    }

    @ApiOperation(value = "根据标题搜索文件(只支持在某个文件夹下搜索)")
    @AutoLog
    @PostMapping("/searchFileByTitle")
    public Result<List<FileDetailVo>> searchFileByTitle(@RequestBody FileSearchRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<FileDetailVo> result = duokeAPI.searchFileByTitle(accessToken, request.getFid(), request.getKey());
        return Result.success(result);
    }

    @PostMapping("/getGroupList")
    @ApiOperation(value = "获取组织结构的子组列表")
    @AutoLog
    public Result<List<GroupVo>> getGroupList(@RequestBody GroupSearchRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<GroupVo> groupList = duokeAPI.getGroupList(request.getFatherUid(), accessToken, request.getIter());
        return Result.success(groupList);
    }

    @PostMapping(value = "/group/user/list")
    @ApiOperation(value = "获取某组织结构下的用户列表及用户的权限")
    @AutoLog
    public Result<List<UserOfGroupVo>> getGroupUserList(@RequestBody FidRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<UserOfGroupVo> userList = duokeAPI.getUserListOfGroup(request.getFid(), accessToken);
        return Result.success(userList);
    }

    @PostMapping(value = "/updateFolder")
    @ApiOperation(value = "文件夹名称/描述修改")
    @AutoLog
    public Result<Boolean> updateFolder(@RequestBody FolderUpdateRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        boolean result = duokeAPI.updateFolder(accessToken, request.getId(), request.getName(), request.getDesc());
        return Result.success(result);
    }

    @PostMapping(value = "/getFolderPermissionList")
    @ApiOperation(value = "获取一个文件夹的授权信息")
    @AutoLog
    public Result<List<FolderPermissionVo>> getFolderPermissionList(@RequestBody FidRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<FolderPermissionVo> permissionList = duokeAPI.getFolderPermissionList(accessToken, request.getFid());
        return Result.success(permissionList);
    }

    @PostMapping(value = "/getUserPermissionOfFolder")
    @ApiOperation(value = "获取一个用户针对一个文件夹/文件的全部授权")
    @AutoLog
    public Result<List<String>> getUserPermissionOfFolder(@RequestBody UserPermissionOfFolderRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        List<String> permissionList = duokeAPI.getUserPermissionOfFolder(accessToken, request.getFid(), request.getUid());
        return Result.success(permissionList);
    }

    @PostMapping(value = "/setPermissionOfFolder")
    @ApiOperation(value = "针对一个文件夹，给某几个组/某几个用户设置权限")
    @AutoLog
    public Result<Boolean> setPermissionOfFolder(@RequestBody SetFolderPermissionRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        boolean result = duokeAPI.setFolderPermission(accessToken, request.getId(), request.getOuids(), request.getPs(), request.getDeadline());
        return Result.success(result);
    }


    @PostMapping(value = "/downloadFileOfFolder")
    @ApiOperation(value = "下载文件夹下的所有文件")
    @AutoLog
    public Result<List<String>> downloadFileOfFolder(@RequestBody FidRequest request) {
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String fid = request.getFid();
        FileOrFolderDTO fileOrFolderDTO = duokeAPI.getFileOrFolderList(accessToken, fid);
        List<FileOrFolderVo> files = fileOrFolderDTO.getFiles();
        List<String> downloadUrls = new ArrayList<>();
        for (FileOrFolderVo file : files) {
            List<String> strings = duokeAPI.downloadFile(accessToken, file.getId());
            downloadUrls.addAll(strings);
        }
        return Result.success(downloadUrls);
    }











}
