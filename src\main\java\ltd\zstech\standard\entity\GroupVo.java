package ltd.zstech.standard.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GroupVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("组ID")
    private String uid;
    @ApiModelProperty("组名称")
    private String name;
    @ApiModelProperty("外部ID（比如钉钉，AD域导入的组ID）")
    private String outUid;
    @ApiModelProperty("组描述")
    private String remark;
    @ApiModelProperty("4 系统增加 5 接口导入 6 AD/LDAP导入 7 企业微信导入 8 钉钉导入 10 批量导入 11 其他系统导入")
    private Integer from;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createAt;
    @ApiModelProperty("在父组中的排序值")
    private Integer orderOfFather;
    @ApiModelProperty("子组列表")
    private List<GroupVo> members;

}
