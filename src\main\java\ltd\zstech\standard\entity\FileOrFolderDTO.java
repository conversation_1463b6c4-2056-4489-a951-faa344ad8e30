package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileOrFolderDTO implements Serializable {
    @ApiModelProperty("文件列表")
    private List<FileOrFolderVo> files;
    @ApiModelProperty("文件夹列表")
    private List<FileOrFolderVo> folders;
}
