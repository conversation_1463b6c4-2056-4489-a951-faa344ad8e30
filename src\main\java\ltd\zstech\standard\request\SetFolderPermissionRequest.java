package ltd.zstech.standard.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SetFolderPermissionRequest {
    @ApiModelProperty(value = "文件夹ID")
    private String id;
    @ApiModelProperty(value = "多个组或者用户ID，用英文逗号分割")
    private String ouids;
    @ApiModelProperty(value = "逗号分割的权限")
    private String ps;
    @ApiModelProperty(value = "授权截至时间，非必填，格式为yyyy-MM-dd HH:mm:ss")
    private String deadline;
}
