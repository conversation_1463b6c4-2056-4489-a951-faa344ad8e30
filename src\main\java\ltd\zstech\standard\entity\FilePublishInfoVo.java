package ltd.zstech.standard.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FilePublishInfoVo {
    @ApiModelProperty("发布时是否选择下载选项")
    private Boolean isDownload;
    @ApiModelProperty("发布时是否选择预览选项")
    private Boolean isPreview;
    @ApiModelProperty("发布时是否选择打印选项")
    private Boolean isPrint;
    @ApiModelProperty("发布截至时间")
    private String period;
    @ApiModelProperty("发布时设置的密码")
    private String password;
    @ApiModelProperty("发布文件的下载地址")
    private String downloadUrl;
    @ApiModelProperty("发布文件的预览地址")
    private String previewUrl;
}
