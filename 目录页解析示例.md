# 目录页解析功能示例

## 示例文档目录页格式

基于您提供的文档图片，以下是系统能够解析的目录页格式示例：

```
                               目    次

前  言 ................................................................ II
1 范围 ................................................................ 1
2 规范性引用文件 ........................................................ 1
3 术语和定义 ........................................................... 1
4 总则 ................................................................ 1
5 职责 ................................................................ 1
6 管理内容及要求 ........................................................ 2
  6.1 设备分级 ........................................................ 2
  6.2 设备停役 ........................................................ 2
  6.3 设备复役 ........................................................ 3
  6.4 报告和记录 ...................................................... 3
附录 .................................................................. 4
附录 A （规范性附录） 设备停复役管理流程图 ............................. 4
附录 B （规范性附录） 【专】设备分级表 ................................. 5
附录 C （规范性附录） 设备停役申请单 ................................... 6
附录 D （规范性附录） 设备复役通知单 ................................... 7
```

## 解析结果示例

系统解析上述目录页后，会返回如下结构化数据：

```json
[
  {
    "title": "前言",
    "level": 1,
    "pageNumber": "II",
    "index": 1,
    "originalText": "前  言 ................................................................ II"
  },
  {
    "title": "1 范围",
    "level": 1,
    "pageNumber": "1",
    "index": 2,
    "originalText": "1 范围 ................................................................ 1"
  },
  {
    "title": "2 规范性引用文件",
    "level": 1,
    "pageNumber": "1",
    "index": 3,
    "originalText": "2 规范性引用文件 ........................................................ 1"
  },
  {
    "title": "6 管理内容及要求",
    "level": 1,
    "pageNumber": "2",
    "index": 7,
    "originalText": "6 管理内容及要求 ........................................................ 2"
  },
  {
    "title": "6.1 设备分级",
    "level": 2,
    "pageNumber": "2",
    "index": 8,
    "originalText": "  6.1 设备分级 ........................................................ 2"
  },
  {
    "title": "6.2 设备停役",
    "level": 2,
    "pageNumber": "2",
    "index": 9,
    "originalText": "  6.2 设备停役 ........................................................ 2"
  },
  {
    "title": "附录",
    "level": 1,
    "pageNumber": "4",
    "index": 12,
    "originalText": "附录 .................................................................. 4"
  },
  {
    "title": "附录 A （规范性附录） 设备停复役管理流程图",
    "level": 1,
    "pageNumber": "4",
    "index": 13,
    "originalText": "附录 A （规范性附录） 设备停复役管理流程图 ............................. 4"
  }
]
```

## 层级关系说明

### 一级目录 (level: 1)
- 主要章节：`1 范围`, `2 规范性引用文件`, `6 管理内容及要求`
- 特殊章节：`前言`, `附录`
- 附录项目：`附录 A`, `附录 B`, `附录 C`, `附录 D`

### 二级目录 (level: 2)
- 子章节：`6.1 设备分级`, `6.2 设备停役`, `6.3 设备复役`, `6.4 报告和记录`
- 通过缩进和编号模式识别

## API调用示例

### 1. 直接获取目录信息

```javascript
// 前端调用示例
const getTableOfContents = async (templateId, formdataId) => {
  try {
    const response = await fetch('/fileOperation/getTableOfContents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: templateId,
        formdataId: formdataId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const toc = result.result.tableOfContents;
      console.log(`找到 ${result.result.totalItems} 个目录项:`);
      
      // 按层级显示目录结构
      toc.forEach(item => {
        const indent = '  '.repeat(item.level - 1);
        console.log(`${indent}${item.title} (页码: ${item.pageNumber})`);
      });
      
      return toc;
    } else {
      console.error('获取目录失败:', result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};
```

### 2. 文档拆分时获取目录

```javascript
// 文档拆分并获取目录信息
const splitDocumentWithTOC = async (templateId, formdataId, subTemplateId) => {
  try {
    const response = await fetch('/fileOperation/splitWithTOC', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: templateId,
        formdataId: formdataId,
        subTemplateId: subTemplateId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const { splitDocuments, tableOfContents, totalSections } = result.result;
      
      console.log(`文档拆分完成，生成 ${splitDocuments.length} 个文档`);
      console.log(`提取 ${tableOfContents.length} 个目录项`);
      
      // 显示目录结构
      console.log('\n目录结构:');
      tableOfContents.forEach(item => {
        const indent = '  '.repeat(item.level - 1);
        console.log(`${indent}${item.title} (页码: ${item.pageNumber})`);
      });
      
      // 显示拆分的文档
      console.log('\n拆分的文档:');
      splitDocuments.forEach(doc => {
        console.log(`- ${doc.fileName} (${doc.filePath})`);
      });
      
      return result.result;
    } else {
      console.error('文档拆分失败:', result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};
```

## 后端服务调用示例

```java
@Service
public class DocumentService {
    
    @Resource
    private FileOperationService fileOperationService;
    
    /**
     * 解析文档目录并构建层级结构
     */
    public Map<String, Object> analyzeDocumentStructure(MultipartFile file) {
        // 获取目录信息
        List<JSONObject> tableOfContents = fileOperationService.getDocumentTableOfContents(file);
        
        // 构建层级结构
        List<Map<String, Object>> hierarchicalToc = buildHierarchicalStructure(tableOfContents);
        
        Map<String, Object> result = new HashMap<>();
        result.put("flatToc", tableOfContents);
        result.put("hierarchicalToc", hierarchicalToc);
        result.put("totalItems", tableOfContents.size());
        
        return result;
    }
    
    /**
     * 构建层级结构
     */
    private List<Map<String, Object>> buildHierarchicalStructure(List<JSONObject> flatToc) {
        List<Map<String, Object>> result = new ArrayList<>();
        Stack<Map<String, Object>> stack = new Stack<>();
        
        for (JSONObject item : flatToc) {
            Map<String, Object> tocItem = new HashMap<>();
            tocItem.put("title", item.getString("title"));
            tocItem.put("pageNumber", item.getString("pageNumber"));
            tocItem.put("level", item.getInteger("level"));
            tocItem.put("children", new ArrayList<>());
            
            int currentLevel = item.getInteger("level");
            
            // 调整栈，确保当前项的父级在栈顶
            while (!stack.isEmpty() && 
                   (Integer) stack.peek().get("level") >= currentLevel) {
                stack.pop();
            }
            
            if (stack.isEmpty()) {
                // 顶级项目
                result.add(tocItem);
            } else {
                // 添加到父级的children中
                Map<String, Object> parent = stack.peek();
                ((List<Map<String, Object>>) parent.get("children")).add(tocItem);
            }
            
            stack.push(tocItem);
        }
        
        return result;
    }
}
```

## 预期输出格式

调用上述方法后，您将得到如下层级结构：

```json
{
  "flatToc": [...], // 扁平的目录列表
  "hierarchicalToc": [
    {
      "title": "前言",
      "pageNumber": "II",
      "level": 1,
      "children": []
    },
    {
      "title": "6 管理内容及要求",
      "pageNumber": "2",
      "level": 1,
      "children": [
        {
          "title": "6.1 设备分级",
          "pageNumber": "2",
          "level": 2,
          "children": []
        },
        {
          "title": "6.2 设备停役",
          "pageNumber": "2",
          "level": 2,
          "children": []
        }
      ]
    }
  ],
  "totalItems": 12
}
```

这样您就可以获得完整的文档目录结构，包括扁平列表和层级关系，便于后续的文档处理和展示。
